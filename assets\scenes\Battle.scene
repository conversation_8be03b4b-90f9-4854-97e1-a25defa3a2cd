[{"__type__": "cc.SceneAsset", "_name": "Battle", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "Battle", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [], "_prefab": {"__id__": 84}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 85}, "_id": "8cb532e6-3f16-46a1-abb6-86cbe350c139"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 5}, {"__id__": 21}, {"__id__": 41}, {"__id__": 61}], "_active": true, "_components": [{"__id__": 81}, {"__id__": 82}, {"__id__": 83}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 360, "y": 640, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5ehzkVAFJBO5GwI7+PljjA"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "7fMcYLJqBPoYcwlMIx/Ojd"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 640, "_near": 0, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1108344832, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "c6d6mwOyxFeYNtiyJFNfxW"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 6}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 5}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 7}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "34YmrlSqdIcq5CMIb1NeXN", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 8}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 15}, {"__id__": 17}, {"__id__": 19}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 200}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 9}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 18}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 20}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 22}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 21}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 23}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "70ngxeI69CIqMfHclzGZVT", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 24}, {"__id__": 26}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}, {"__id__": 37}, {"__id__": 39}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 300}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 30}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 60, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_string"], "value": "哥布林"}, {"__type__": "cc.TargetInfo", "localID": ["RNSV8y5rVBprtoddV0/jt1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 150, "g": 100, "b": 50, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["TpCZtnXeJPOE7wYelUzI9V"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 42}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 41}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 43}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "f14J+hAW5K3bi32t+8Ugt0", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 44}, {"__id__": 46}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 100}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 50}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 40, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 54}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_string"], "value": "兽人"}, {"__type__": "cc.TargetInfo", "localID": ["RNSV8y5rVBprtoddV0/jt1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 100, "g": 150, "b": 100, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["TpCZtnXeJPOE7wYelUzI9V"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 62}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 61}, "asset": {"__uuid__": "f0a60ab6-5422-4579-8b4f-5506b9220656", "__expectedType__": "cc.Prefab"}, "fileId": "f45a6M9v1Np59WrUchSnAH", "instance": {"__id__": 63}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "6c5M/xOlNDE6F8TE3SF9W9", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 64}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 71}, {"__id__": 73}, {"__id__": 75}, {"__id__": 77}, {"__id__": 79}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_name"], "value": "CharacterUnit"}, {"__type__": "cc.TargetInfo", "localID": ["f45a6M9v1Np59WrUchSnAH"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -100}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 60, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["/FnUvy8/fTgCY4gWvzvUQx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 72}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["EdW+Utp0i98o3JTuWeSCHA"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 74}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["tarXi7D838Jzx06STpYmID"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 48.935546875, "height": 31.5}}, {"__type__": "cc.TargetInfo", "localID": ["NJ6KCUUnRZRDvMXfYn8qZp"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_string"], "value": "哥布林"}, {"__type__": "cc.TargetInfo", "localID": ["RNSV8y5rVBprtoddV0/jt1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 80}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 150, "g": 100, "b": 50, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["TpCZtnXeJPOE7wYelUzI9V"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 720, "height": 1280}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c85ac1zChFhoIYbwl9zUth"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 4}, "_alignCanvasWithScreen": true, "_id": "ab96bMsnNHwZnZkFMucVts"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "92KM2X781EH4C1Np0g+a3Y"}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "8cb532e6-3f16-46a1-abb6-86cbe350c139", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 5}, {"__id__": 21}, {"__id__": 41}, {"__id__": 61}]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 86}, "shadows": {"__id__": 87}, "_skybox": {"__id__": 88}, "fog": {"__id__": 89}, "octree": {"__id__": 90}, "skin": {"__id__": 91}, "lightProbeInfo": {"__id__": 92}, "postSettings": {"__id__": 93}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.5, "z": 0.8, "w": 1}, "_skyIllumLDR": 20000, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 76}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": true, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]