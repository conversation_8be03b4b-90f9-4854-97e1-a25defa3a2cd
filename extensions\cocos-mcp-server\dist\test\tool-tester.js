"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolTester = void 0;
class ToolTester {
    constructor() {
        this.results = [];
    }
    async runTest(tool, method, params) {
        const startTime = Date.now();
        const result = {
            tool,
            method,
            success: false,
            time: 0
        };
        try {
            const response = await Editor.Message.request(tool, method, params);
            result.success = true;
            result.result = response;
        }
        catch (error) {
            result.success = false;
            result.error = error instanceof Error ? error.message : String(error);
        }
        result.time = Date.now() - startTime;
        this.results.push(result);
        return result;
    }
    async testSceneOperations() {
        console.log('Testing Scene Operations...');
        // Test node creation (this is the main scene operation that works)
        const createResult = await this.runTest('scene', 'create-node', {
            name: 'TestNode',
            type: 'cc.Node'
        });
        if (createResult.success && createResult.result) {
            const nodeUuid = createResult.result;
            // Test query node info
            await this.runTest('scene', 'query-node-info', nodeUuid);
            // Test remove node
            await this.runTest('scene', 'remove-node', nodeUuid);
        }
        // Test execute scene script
        await this.runTest('scene', 'execute-scene-script', {
            name: 'cocos-mcp-server',
            method: 'test-method',
            args: []
        });
    }
    async testNodeOperations() {
        console.log('Testing Node Operations...');
        // Create a test node first
        const createResult = await this.runTest('scene', 'create-node', {
            name: 'TestNodeForOps',
            type: 'cc.Node'
        });
        if (createResult.success && createResult.result) {
            const nodeUuid = createResult.result;
            // Test set property
            await this.runTest('scene', 'set-property', {
                uuid: nodeUuid,
                path: 'position',
                dump: {
                    type: 'cc.Vec3',
                    value: { x: 100, y: 200, z: 0 }
                }
            });
            // Test add component
            await this.runTest('scene', 'add-component', {
                uuid: nodeUuid,
                component: 'cc.Sprite'
            });
            // Clean up
            await this.runTest('scene', 'remove-node', nodeUuid);
        }
    }
    async testAssetOperations() {
        console.log('Testing Asset Operations...');
        // Test asset list
        await this.runTest('asset-db', 'query-assets', {
            pattern: '**/*.png',
            ccType: 'cc.ImageAsset'
        });
        // Test query asset by path
        await this.runTest('asset-db', 'query-path', 'db://assets');
        // Test query asset by uuid (using a valid uuid format)
        await this.runTest('asset-db', 'query-uuid', 'db://assets');
    }
    async testProjectOperations() {
        console.log('Testing Project Operations...');
        // Test open project settings
        await this.runTest('project', 'open-settings', {});
        // Test query project settings
        const projectName = await this.runTest('project', 'query-setting', 'name');
        if (projectName.success) {
            console.log('Project name:', projectName.result);
        }
    }
    async runAllTests() {
        this.results = [];
        await this.testSceneOperations();
        await this.testNodeOperations();
        await this.testAssetOperations();
        await this.testProjectOperations();
        return this.getTestReport();
    }
    getTestReport() {
        const total = this.results.length;
        const passed = this.results.filter(r => r.success).length;
        const failed = total - passed;
        return {
            summary: {
                total,
                passed,
                failed,
                passRate: total > 0 ? (passed / total * 100).toFixed(2) + '%' : '0%'
            },
            results: this.results,
            grouped: this.groupResultsByTool()
        };
    }
    groupResultsByTool() {
        const grouped = {};
        for (const result of this.results) {
            if (!grouped[result.tool]) {
                grouped[result.tool] = [];
            }
            grouped[result.tool].push(result);
        }
        return grouped;
    }
}
exports.ToolTester = ToolTester;
//# sourceMappingURL=data:application/json;base64,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