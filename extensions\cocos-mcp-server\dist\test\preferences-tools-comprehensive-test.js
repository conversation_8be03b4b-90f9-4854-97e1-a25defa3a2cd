"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PreferencesToolsComprehensiveTester = void 0;
class PreferencesToolsComprehensiveTester {
    constructor() {
        this.results = [];
        // Import the PreferencesTools class for testing
        try {
            const { PreferencesTools } = require('../tools/preferences-tools');
            this.preferencesTools = new PreferencesTools();
        }
        catch (error) {
            console.error('Failed to import PreferencesTools:', error);
        }
    }
    async runTest(testName, tool, action, params, expectedBehavior, aiPromptClarity = 'good') {
        const startTime = Date.now();
        const result = {
            testName,
            tool,
            action,
            success: false,
            time: 0,
            parameters: params,
            expectedBehavior,
            aiPromptClarity
        };
        try {
            const response = await this.preferencesTools.execute(tool, params);
            result.success = response.success;
            result.result = response;
            if (!response.success) {
                result.error = response.error || 'Unknown error';
            }
        }
        catch (error) {
            result.success = false;
            result.error = error instanceof Error ? error.message : String(error);
        }
        result.time = Date.now() - startTime;
        this.results.push(result);
        console.log(`Test: ${testName} - ${result.success ? 'PASS' : 'FAIL'} (${result.time}ms)`);
        if (!result.success) {
            console.log(`  Error: ${result.error}`);
        }
        return result;
    }
    // Test 1: preferences_manage tool - open_panel action
    async testPreferencesManageOpenPanel() {
        console.log('\n=== Testing preferences_manage - open_panel ===');
        // Test 1.1: Open panel without specific tab
        await this.runTest('Open preferences panel (default)', 'preferences_manage', 'open_panel', { action: 'open_panel' }, 'Should open preferences panel on default tab', 'excellent');
        // Test 1.2: Open panel with each valid tab
        const validTabs = ['general', 'external-tools', 'data-editor', 'laboratory', 'extensions', 'preview', 'console', 'native', 'builder'];
        for (const tab of validTabs) {
            await this.runTest(`Open preferences panel on ${tab} tab`, 'preferences_manage', 'open_panel', { action: 'open_panel', tab }, `Should open preferences panel on ${tab} tab`, 'excellent');
        }
        // Test 1.3: Test with invalid tab
        await this.runTest('Open preferences panel with invalid tab', 'preferences_manage', 'open_panel', { action: 'open_panel', tab: 'invalid-tab' }, 'Should handle invalid tab gracefully or show error', 'good');
    }
    // Test 2: preferences_manage tool - get_config action
    async testPreferencesManageGetConfig() {
        console.log('\n=== Testing preferences_manage - get_config ===');
        const categories = ['general', 'external-tools', 'data-editor', 'laboratory', 'extensions', 'preview', 'console', 'native', 'builder'];
        const scopes = ['global', 'local', 'default'];
        // Test 2.1: Get entire category config for each scope
        for (const category of categories) {
            for (const scope of scopes) {
                await this.runTest(`Get ${category} config (${scope} scope)`, 'preferences_manage', 'get_config', { action: 'get_config', category, scope }, `Should return ${category} configuration for ${scope} scope`, 'excellent');
            }
        }
        // Test 2.2: Get specific config paths
        const testPaths = [
            { category: 'general', path: 'language' },
            { category: 'general', path: 'theme' },
            { category: 'console', path: 'level' },
            { category: 'preview', path: 'resolution' }
        ];
        for (const testPath of testPaths) {
            await this.runTest(`Get specific config: ${testPath.category}.${testPath.path}`, 'preferences_manage', 'get_config', { action: 'get_config', category: testPath.category, path: testPath.path }, `Should return specific configuration value for ${testPath.category}.${testPath.path}`, 'excellent');
        }
        // Test 2.3: Test with nested paths (dot notation)
        await this.runTest('Get nested config path', 'preferences_manage', 'get_config', { action: 'get_config', category: 'general', path: 'editor.fontSize' }, 'Should handle nested path with dot notation', 'good');
        // Test 2.4: Test error cases
        await this.runTest('Get config with invalid category', 'preferences_manage', 'get_config', { action: 'get_config', category: 'invalid-category' }, 'Should return error for invalid category', 'good');
        await this.runTest('Get config without required category', 'preferences_manage', 'get_config', { action: 'get_config' }, 'Should return error for missing required category parameter', 'good');
    }
    // Test 3: preferences_manage tool - set_config action
    async testPreferencesManageSetConfig() {
        console.log('\n=== Testing preferences_manage - set_config ===');
        // Test 3.1: Set different data types
        const testValues = [
            { category: 'general', path: 'test.string', value: 'test-value', type: 'string' },
            { category: 'general', path: 'test.number', value: 42, type: 'number' },
            { category: 'general', path: 'test.boolean', value: true, type: 'boolean' },
            { category: 'general', path: 'test.object', value: { key: 'value', nested: { prop: 123 } }, type: 'object' },
            { category: 'general', path: 'test.array', value: [1, 2, 3, 'test'], type: 'array' }
        ];
        for (const testValue of testValues) {
            await this.runTest(`Set ${testValue.type} value: ${testValue.path}`, 'preferences_manage', 'set_config', {
                action: 'set_config',
                category: testValue.category,
                path: testValue.path,
                value: testValue.value
            }, `Should set ${testValue.type} value for ${testValue.path}`, 'excellent');
        }
        // Test 3.2: Test with different scopes
        for (const scope of ['global', 'local']) {
            await this.runTest(`Set config with ${scope} scope`, 'preferences_manage', 'set_config', {
                action: 'set_config',
                category: 'general',
                path: 'test.scope',
                value: `${scope}-value`,
                scope
            }, `Should set configuration in ${scope} scope`, 'excellent');
        }
        // Test 3.3: Test error cases
        await this.runTest('Set config with missing required parameters', 'preferences_manage', 'set_config', { action: 'set_config', category: 'general' }, 'Should return error for missing required path and value parameters', 'good');
        await this.runTest('Set config with invalid category', 'preferences_manage', 'set_config', { action: 'set_config', category: 'invalid-category', path: 'test', value: 'test' }, 'Should return error for invalid category', 'good');
    }
    // Test 4: preferences_manage tool - reset_config action
    async testPreferencesManageResetConfig() {
        console.log('\n=== Testing preferences_manage - reset_config ===');
        const categories = ['general', 'external-tools', 'data-editor'];
        // Test 4.1: Reset each category
        for (const category of categories) {
            await this.runTest(`Reset ${category} to defaults`, 'preferences_manage', 'reset_config', { action: 'reset_config', category }, `Should reset ${category} preferences to default values`, 'excellent');
        }
        // Test 4.2: Reset with different scopes
        for (const scope of ['global', 'local']) {
            await this.runTest(`Reset general config (${scope} scope)`, 'preferences_manage', 'reset_config', { action: 'reset_config', category: 'general', scope }, `Should reset general preferences in ${scope} scope`, 'excellent');
        }
        // Test 4.3: Test error cases
        await this.runTest('Reset config with invalid category', 'preferences_manage', 'reset_config', { action: 'reset_config', category: 'invalid-category' }, 'Should return error for invalid category', 'good');
        await this.runTest('Reset config without required category', 'preferences_manage', 'reset_config', { action: 'reset_config' }, 'Should return error for missing required category parameter', 'good');
    }
    // Test 5: preferences_query tool - get_all action
    async testPreferencesQueryGetAll() {
        console.log('\n=== Testing preferences_query - get_all ===');
        // Test 5.1: Get all preferences with different scopes
        for (const scope of ['global', 'local', 'default']) {
            await this.runTest(`Get all preferences (${scope} scope)`, 'preferences_query', 'get_all', { action: 'get_all', scope }, `Should return all preferences for ${scope} scope`, 'excellent');
        }
        // Test 5.2: Get specific categories
        const categoryTests = [
            { categories: ['general'] },
            { categories: ['general', 'console'] },
            { categories: ['external-tools', 'data-editor', 'preview'] }
        ];
        for (const test of categoryTests) {
            await this.runTest(`Get specific categories: ${test.categories.join(', ')}`, 'preferences_query', 'get_all', { action: 'get_all', categories: test.categories }, `Should return preferences for specified categories: ${test.categories.join(', ')}`, 'excellent');
        }
        // Test 5.3: Combine scope and categories
        await this.runTest('Get specific categories with local scope', 'preferences_query', 'get_all', { action: 'get_all', scope: 'local', categories: ['general', 'preview'] }, 'Should return local scope preferences for general and preview categories', 'excellent');
        // Test 5.4: Test with invalid categories
        await this.runTest('Get all with invalid categories', 'preferences_query', 'get_all', { action: 'get_all', categories: ['invalid-category', 'general'] }, 'Should handle invalid categories gracefully and return valid ones', 'good');
    }
    // Test 6: preferences_query tool - list_categories action
    async testPreferencesQueryListCategories() {
        console.log('\n=== Testing preferences_query - list_categories ===');
        // Test 6.1: List all categories
        await this.runTest('List all preference categories', 'preferences_query', 'list_categories', { action: 'list_categories' }, 'Should return list of all available preference categories with descriptions', 'excellent');
    }
    // Test 7: preferences_query tool - search_settings action
    async testPreferencesQuerySearchSettings() {
        console.log('\n=== Testing preferences_query - search_settings ===');
        // Test 7.1: Search with different keywords
        const searchKeywords = [
            'language',
            'theme',
            'font',
            'path',
            'console',
            'debug'
        ];
        for (const keyword of searchKeywords) {
            await this.runTest(`Search settings for "${keyword}"`, 'preferences_query', 'search_settings', { action: 'search_settings', keyword }, `Should find settings containing keyword "${keyword}"`, 'excellent');
        }
        // Test 7.2: Search with and without values
        await this.runTest('Search with includeValues=true', 'preferences_query', 'search_settings', { action: 'search_settings', keyword: 'language', includeValues: true }, 'Should return search results with current values included', 'excellent');
        await this.runTest('Search with includeValues=false', 'preferences_query', 'search_settings', { action: 'search_settings', keyword: 'language', includeValues: false }, 'Should return search results without current values', 'excellent');
        // Test 7.3: Test edge cases
        await this.runTest('Search with empty keyword', 'preferences_query', 'search_settings', { action: 'search_settings', keyword: '' }, 'Should handle empty keyword appropriately', 'fair');
        await this.runTest('Search with non-existent keyword', 'preferences_query', 'search_settings', { action: 'search_settings', keyword: 'non-existent-setting-xyz123' }, 'Should return empty results for non-existent keyword', 'good');
        await this.runTest('Search without required keyword', 'preferences_query', 'search_settings', { action: 'search_settings' }, 'Should return error for missing required keyword parameter', 'good');
    }
    // Test 8: preferences_backup tool - export action
    async testPreferencesBackupExport() {
        console.log('\n=== Testing preferences_backup - export ===');
        // Test 8.1: Export all preferences
        await this.runTest('Export all preferences (default)', 'preferences_backup', 'export', { action: 'export' }, 'Should export all preferences with default settings', 'excellent');
        // Test 8.2: Export with different scopes
        for (const scope of ['global', 'local']) {
            await this.runTest(`Export preferences (${scope} scope)`, 'preferences_backup', 'export', { action: 'export', scope }, `Should export preferences from ${scope} scope`, 'excellent');
        }
        // Test 8.3: Export specific categories
        await this.runTest('Export specific categories', 'preferences_backup', 'export', { action: 'export', categories: ['general', 'console', 'preview'] }, 'Should export only specified categories', 'excellent');
        // Test 8.4: Export with includeDefaults
        await this.runTest('Export with defaults included', 'preferences_backup', 'export', { action: 'export', includeDefaults: true }, 'Should export preferences including default values', 'excellent');
        await this.runTest('Export without defaults', 'preferences_backup', 'export', { action: 'export', includeDefaults: false }, 'Should export preferences without default values', 'excellent');
        // Test 8.5: Comprehensive export test
        await this.runTest('Export with all parameters', 'preferences_backup', 'export', {
            action: 'export',
            categories: ['general', 'external-tools'],
            scope: 'global',
            includeDefaults: true
        }, 'Should export specified categories from global scope with defaults', 'excellent');
    }
    // Test 9: preferences_backup tool - validate_backup action
    async testPreferencesBackupValidate() {
        console.log('\n=== Testing preferences_backup - validate_backup ===');
        // Test 9.1: Valid backup data
        const validBackupData = {
            metadata: {
                exportDate: new Date().toISOString(),
                scope: 'global',
                includeDefaults: false,
                cocosVersion: '3.8.0',
                exportedCategories: ['general', 'console']
            },
            preferences: {
                general: {
                    language: 'en',
                    theme: 'dark'
                },
                console: {
                    level: 'info'
                }
            }
        };
        await this.runTest('Validate valid backup data', 'preferences_backup', 'validate_backup', { action: 'validate_backup', backupData: validBackupData }, 'Should validate valid backup data successfully', 'excellent');
        // Test 9.2: Backup data with warnings
        const backupWithWarnings = {
            preferences: {
                general: {
                    language: 'en'
                }
            }
            // Missing metadata
        };
        await this.runTest('Validate backup data with warnings', 'preferences_backup', 'validate_backup', { action: 'validate_backup', backupData: backupWithWarnings }, 'Should validate backup data and report warnings for missing metadata', 'good');
        // Test 9.3: Invalid backup data
        const invalidBackupData = [
            { description: 'null data', data: null },
            { description: 'string data', data: 'invalid' },
            { description: 'number data', data: 123 },
            { description: 'missing preferences', data: { metadata: {} } },
            { description: 'invalid preferences type', data: { preferences: 'invalid' } },
            { description: 'empty preferences', data: { preferences: {} } }
        ];
        for (const testCase of invalidBackupData) {
            await this.runTest(`Validate invalid backup: ${testCase.description}`, 'preferences_backup', 'validate_backup', { action: 'validate_backup', backupData: testCase.data }, `Should detect invalid backup data: ${testCase.description}`, 'good');
        }
        // Test 9.4: Missing required parameter
        await this.runTest('Validate without backup data parameter', 'preferences_backup', 'validate_backup', { action: 'validate_backup' }, 'Should return error for missing required backupData parameter', 'good');
    }
    // Test 10: Error handling and edge cases
    async testErrorHandling() {
        console.log('\n=== Testing Error Handling ===');
        // Test 10.1: Invalid tool names
        try {
            await this.preferencesTools.execute('invalid_tool', { action: 'test' });
        }
        catch (error) {
            this.results.push({
                testName: 'Invalid tool name',
                tool: 'invalid_tool',
                action: 'test',
                success: false,
                error: error instanceof Error ? error.message : String(error),
                time: 0,
                expectedBehavior: 'Should throw error for invalid tool name',
                aiPromptClarity: 'good'
            });
        }
        // Test 10.2: Invalid actions for each tool
        const invalidActionTests = [
            { tool: 'preferences_manage', action: 'invalid_action' },
            { tool: 'preferences_query', action: 'invalid_action' },
            { tool: 'preferences_backup', action: 'invalid_action' }
        ];
        for (const test of invalidActionTests) {
            await this.runTest(`Invalid action for ${test.tool}`, test.tool, test.action, { action: test.action }, `Should return error for invalid action: ${test.action}`, 'good');
        }
        // Test 10.3: Invalid scope values
        await this.runTest('Invalid scope value', 'preferences_manage', 'get_config', { action: 'get_config', category: 'general', scope: 'invalid-scope' }, 'Should handle invalid scope value appropriately', 'fair');
    }
    // Main test runner
    async runComprehensiveTests() {
        console.log('🚀 Starting Comprehensive Preferences Tools Test Suite');
        console.log('='.repeat(60));
        this.results = [];
        const startTime = Date.now();
        try {
            // Run all test categories
            await this.testPreferencesManageOpenPanel();
            await this.testPreferencesManageGetConfig();
            await this.testPreferencesManageSetConfig();
            await this.testPreferencesManageResetConfig();
            await this.testPreferencesQueryGetAll();
            await this.testPreferencesQueryListCategories();
            await this.testPreferencesQuerySearchSettings();
            await this.testPreferencesBackupExport();
            await this.testPreferencesBackupValidate();
            await this.testErrorHandling();
        }
        catch (error) {
            console.error('Test suite failed:', error);
        }
        const totalTime = Date.now() - startTime;
        return this.generateComprehensiveReport(totalTime);
    }
    generateComprehensiveReport(totalTime) {
        const total = this.results.length;
        const passed = this.results.filter(r => r.success).length;
        const failed = total - passed;
        // Group results by tool and action
        const groupedResults = this.groupResultsByToolAndAction();
        // Analyze AI prompt clarity
        const clarityAnalysis = this.analyzeClarityRatings();
        // Performance analysis
        const performanceAnalysis = this.analyzePerformance();
        const report = {
            summary: {
                totalTests: total,
                passed,
                failed,
                passRate: total > 0 ? ((passed / total) * 100).toFixed(2) + '%' : '0%',
                totalTime: totalTime + 'ms',
                averageTestTime: total > 0 ? (totalTime / total).toFixed(2) + 'ms' : '0ms'
            },
            toolCoverage: {
                preferences_manage: {
                    actions: ['open_panel', 'get_config', 'set_config', 'reset_config'],
                    tested: this.getTestedActions('preferences_manage'),
                    coverage: this.calculateActionCoverage('preferences_manage', 4)
                },
                preferences_query: {
                    actions: ['get_all', 'list_categories', 'search_settings'],
                    tested: this.getTestedActions('preferences_query'),
                    coverage: this.calculateActionCoverage('preferences_query', 3)
                },
                preferences_backup: {
                    actions: ['export', 'validate_backup'],
                    tested: this.getTestedActions('preferences_backup'),
                    coverage: this.calculateActionCoverage('preferences_backup', 2)
                }
            },
            clarityAnalysis,
            performanceAnalysis,
            detailedResults: groupedResults,
            errorAnalysis: this.analyzeErrors(),
            recommendations: this.generateRecommendations()
        };
        this.printDetailedReport(report);
        return report;
    }
    groupResultsByToolAndAction() {
        const grouped = {};
        for (const result of this.results) {
            if (!grouped[result.tool]) {
                grouped[result.tool] = {};
            }
            if (!grouped[result.tool][result.action]) {
                grouped[result.tool][result.action] = [];
            }
            grouped[result.tool][result.action].push(result);
        }
        return grouped;
    }
    getTestedActions(tool) {
        const actions = new Set();
        for (const result of this.results) {
            if (result.tool === tool) {
                actions.add(result.action);
            }
        }
        return Array.from(actions);
    }
    calculateActionCoverage(tool, totalActions) {
        const testedActions = this.getTestedActions(tool).length;
        return ((testedActions / totalActions) * 100).toFixed(0) + '%';
    }
    analyzeClarityRatings() {
        const ratings = this.results.map(r => r.aiPromptClarity);
        const counts = {
            excellent: ratings.filter(r => r === 'excellent').length,
            good: ratings.filter(r => r === 'good').length,
            fair: ratings.filter(r => r === 'fair').length,
            poor: ratings.filter(r => r === 'poor').length
        };
        return {
            distribution: counts,
            averageScore: this.calculateAverageClarity(ratings),
            total: ratings.length
        };
    }
    calculateAverageClarity(ratings) {
        const scoreMap = { excellent: 4, good: 3, fair: 2, poor: 1 };
        const totalScore = ratings.reduce((sum, rating) => sum + scoreMap[rating], 0);
        return ratings.length > 0 ? (totalScore / ratings.length) : 0;
    }
    analyzePerformance() {
        const times = this.results.map(r => r.time).filter(t => t > 0);
        if (times.length === 0)
            return { average: 0, min: 0, max: 0, total: 0 };
        return {
            average: (times.reduce((sum, time) => sum + time, 0) / times.length).toFixed(2) + 'ms',
            min: Math.min(...times) + 'ms',
            max: Math.max(...times) + 'ms',
            total: times.reduce((sum, time) => sum + time, 0) + 'ms'
        };
    }
    analyzeErrors() {
        const errors = this.results.filter(r => !r.success);
        const errorTypes = {};
        for (const error of errors) {
            const errorMsg = error.error || 'Unknown error';
            const errorType = this.categorizeError(errorMsg);
            errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        }
        return {
            totalErrors: errors.length,
            errorTypes,
            commonErrors: Object.entries(errorTypes)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 5)
        };
    }
    categorizeError(errorMsg) {
        if (errorMsg.includes('Unknown tool'))
            return 'Invalid Tool';
        if (errorMsg.includes('Unknown') && errorMsg.includes('action'))
            return 'Invalid Action';
        if (errorMsg.includes('required'))
            return 'Missing Parameter';
        if (errorMsg.includes('invalid') || errorMsg.includes('Invalid'))
            return 'Invalid Parameter';
        if (errorMsg.includes('Failed to'))
            return 'Operation Failed';
        return 'Other';
    }
    generateRecommendations() {
        const recommendations = [];
        const failureRate = (this.results.filter(r => !r.success).length / this.results.length) * 100;
        if (failureRate > 20) {
            recommendations.push('High failure rate detected. Review parameter validation and error handling.');
        }
        const clarityAnalysis = this.analyzeClarityRatings();
        if (clarityAnalysis.averageScore < 3) {
            recommendations.push('AI prompt clarity could be improved. Consider more descriptive parameter descriptions.');
        }
        const errors = this.analyzeErrors();
        if (errors.errorTypes['Missing Parameter'] > 3) {
            recommendations.push('Many missing parameter errors. Consider adding better default values or validation.');
        }
        if (errors.errorTypes['Invalid Parameter'] > 3) {
            recommendations.push('Multiple invalid parameter errors. Strengthen input validation.');
        }
        if (recommendations.length === 0) {
            recommendations.push('Overall test results look good. Consider adding more edge case tests.');
        }
        return recommendations;
    }
    printDetailedReport(report) {
        console.log('\n' + '='.repeat(60));
        console.log('📊 COMPREHENSIVE PREFERENCES TOOLS TEST REPORT');
        console.log('='.repeat(60));
        console.log('\n📈 SUMMARY:');
        console.log(`  Total Tests: ${report.summary.totalTests}`);
        console.log(`  Passed: ${report.summary.passed}`);
        console.log(`  Failed: ${report.summary.failed}`);
        console.log(`  Pass Rate: ${report.summary.passRate}`);
        console.log(`  Total Time: ${report.summary.totalTime}`);
        console.log(`  Average Test Time: ${report.summary.averageTestTime}`);
        console.log('\n🎯 TOOL COVERAGE:');
        for (const [tool, coverage] of Object.entries(report.toolCoverage)) {
            const toolCoverage = coverage;
            console.log(`  ${tool}:`);
            console.log(`    Actions: ${toolCoverage.actions.join(', ')}`);
            console.log(`    Tested: ${toolCoverage.tested.join(', ')}`);
            console.log(`    Coverage: ${toolCoverage.coverage}`);
        }
        console.log('\n🧠 AI PROMPT CLARITY ANALYSIS:');
        console.log(`  Excellent: ${report.clarityAnalysis.distribution.excellent}`);
        console.log(`  Good: ${report.clarityAnalysis.distribution.good}`);
        console.log(`  Fair: ${report.clarityAnalysis.distribution.fair}`);
        console.log(`  Poor: ${report.clarityAnalysis.distribution.poor}`);
        console.log(`  Average Score: ${report.clarityAnalysis.averageScore.toFixed(2)}/4.0`);
        console.log('\n⚡ PERFORMANCE ANALYSIS:');
        console.log(`  Average Time: ${report.performanceAnalysis.average}`);
        console.log(`  Min Time: ${report.performanceAnalysis.min}`);
        console.log(`  Max Time: ${report.performanceAnalysis.max}`);
        console.log(`  Total Time: ${report.performanceAnalysis.total}`);
        if (report.errorAnalysis.totalErrors > 0) {
            console.log('\n❌ ERROR ANALYSIS:');
            console.log(`  Total Errors: ${report.errorAnalysis.totalErrors}`);
            console.log('  Common Error Types:');
            for (const [errorType, count] of report.errorAnalysis.commonErrors) {
                console.log(`    ${errorType}: ${count}`);
            }
        }
        console.log('\n💡 RECOMMENDATIONS:');
        for (const recommendation of report.recommendations) {
            console.log(`  • ${recommendation}`);
        }
        console.log('\n' + '='.repeat(60));
        console.log('Test completed successfully! ✅');
        console.log('='.repeat(60));
    }
}
exports.PreferencesToolsComprehensiveTester = PreferencesToolsComprehensiveTester;
// Export the test class for external use
exports.default = PreferencesToolsComprehensiveTester;
//# sourceMappingURL=data:application/json;base64,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