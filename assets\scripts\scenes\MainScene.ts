import { _decorator, Component, Node, find, director, input, Input, EventKeyboard, KeyCode } from 'cc';
import { GameManager } from '../managers/GameManager';
import { UIManager } from '../managers/UIManager';
import { EventManager } from '../managers/EventManager';
import { AreaSelectionPanel } from '../ui/panels/AreaSelectionPanel';
import { CharacterInfoPanel } from '../ui/panels/CharacterInfoPanel';

const { ccclass, property } = _decorator;

/**
 * 主场景控制器
 * 管理主界面的整体逻辑和各个面板的协调
 */
@ccclass('MainScene')
export class MainScene extends Component {

    @property(Node)
    leftPanel: Node = null!;

    @property(Node)
    rightPanel: Node = null!;

    @property(Node)
    centerContent: Node = null!;

    // UI面板组件
    private _areaSelectionPanel: AreaSelectionPanel = null!;
    private _characterInfoPanel: CharacterInfoPanel = null!;

    // 当前状态
    private _currentArea: string = '';
    private _currentFunction: string = '';

    protected onLoad(): void {
        console.log('🎮 MainScene: 主场景加载');
        this.initializeComponents();
        this.registerEventListeners();
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🎮 MainScene: 主场景启动');
        this.initializeScene();
        this.showTestInstructions();
    }

    /**
     * 初始化组件
     */
    private initializeComponents(): void {
        // 获取面板组件
        if (this.leftPanel) {
            this._areaSelectionPanel = this.leftPanel.getComponent(AreaSelectionPanel);
        }

        if (this.rightPanel) {
            this._characterInfoPanel = this.rightPanel.getComponent(CharacterInfoPanel);
        }

        // 如果没有通过属性设置，尝试通过查找获取
        if (!this._areaSelectionPanel) {
            const leftPanelNode = find('Canvas/MainUI/LeftPanel');
            if (leftPanelNode) {
                this._areaSelectionPanel = leftPanelNode.getComponent(AreaSelectionPanel);
            }
        }

        if (!this._characterInfoPanel) {
            const rightPanelNode = find('Canvas/MainUI/RightPanel');
            if (rightPanelNode) {
                this._characterInfoPanel = rightPanelNode.getComponent(CharacterInfoPanel);
            }
        }
    }

    /**
     * 注册事件监听
     */
    private registerEventListeners(): void {
        const eventManager = EventManager.getInstance();

        // 监听区域选择事件
        eventManager.on('area-selected', this.onAreaSelected, this);

        // 监听角色功能选择事件
        eventManager.on('character-function-selected', this.onCharacterFunctionSelected, this);

        // 监听角色信息更新事件
        eventManager.on('character-info-updated', this.onCharacterInfoUpdated, this);
    }

    /**
     * 初始化场景
     */
    private initializeScene(): void {
        // 设置默认状态
        this.updateCenterContent('battle', 'detail_info');

        // 加载角色数据
        this.loadCharacterData();
    }

    /**
     * 区域选择事件处理
     */
    private onAreaSelected(eventData: any): void {
        console.log('🗺️ 区域选择:', eventData);

        this._currentArea = eventData.areaId;
        this.updateCenterContent(this._currentArea, this._currentFunction);

        // 这里可以加载对应区域的数据
        this.loadAreaData(eventData.areaId);
    }

    /**
     * 角色功能选择事件处理
     */
    private onCharacterFunctionSelected(eventData: any): void {
        console.log('👤 功能选择:', eventData);

        this._currentFunction = eventData.functionId;
        this.updateCenterContent(this._currentArea, this._currentFunction);

        // 根据选择的功能显示对应内容
        this.showFunctionContent(eventData.functionId);
    }

    /**
     * 角色信息更新事件处理
     */
    private onCharacterInfoUpdated(characterData: any): void {
        console.log('📊 角色信息更新:', characterData);
        // 这里可以处理角色信息更新后的逻辑
    }

    /**
     * 更新中心内容区域
     */
    private updateCenterContent(areaId: string, functionId: string): void {
        console.log(`🎯 更新中心内容: 区域=${areaId}, 功能=${functionId}`);

        // 这里可以根据当前选择的区域和功能来显示不同的内容
        if (this.centerContent) {
            this.showCurrentStatus(areaId, functionId);
        }
    }

    /**
     * 显示当前状态信息
     */
    private showCurrentStatus(areaId: string, functionId: string): void {
        console.log(`当前状态: 区域=${areaId}, 功能=${functionId}`);
    }

    /**
     * 加载区域数据
     */
    private loadAreaData(areaId: string): void {
        console.log(`📦 加载区域数据: ${areaId}`);
    }

    /**
     * 显示功能内容
     */
    private showFunctionContent(functionId: string): void {
        console.log(`🔧 显示功能内容: ${functionId}`);

        switch (functionId) {
            case 'detail_info':
                this.showDetailInfo();
                break;
            case 'inventory':
                this.showInventory();
                break;
            case 'equipment':
                this.showEquipment();
                break;
            case 'level':
                this.showLevel();
                break;
            case 'skill':
                this.showSkill();
                break;
        }
    }

    /**
     * 显示详细信息
     */
    private showDetailInfo(): void {
        console.log('📋 显示角色详细信息');
    }

    /**
     * 显示背包
     */
    private showInventory(): void {
        console.log('🎒 显示背包');
    }

    /**
     * 显示装备
     */
    private showEquipment(): void {
        console.log('⚔️ 显示装备');
    }

    /**
     * 显示等级
     */
    private showLevel(): void {
        console.log('📈 显示等级');
    }

    /**
     * 显示技能
     */
    private showSkill(): void {
        console.log('🔮 显示技能');
    }

    /**
     * 加载角色数据
     */
    private loadCharacterData(): void {
        console.log('👤 加载角色数据');

        // 模拟角色数据
        const characterData = {
            name: '武侠英雄',
            level: 1,
            experience: 0,
            health: 100,
            mana: 50,
            strength: 10,
            agility: 8,
            intelligence: 12
        };

        // 更新角色信息面板
        if (this._characterInfoPanel) {
            this._characterInfoPanel.updateCharacterInfo(characterData);
        }
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🎮 ========== 主场景UI测试 ==========');
        console.log('📍 当前场景: Main (主界面)');
        console.log('🖱️ 点击左侧按钮选择区域');
        console.log('🖱️ 点击右侧按钮选择功能');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 切换到 Launch 场景');
        console.log('   按 2 键 - 切换到 Main 场景');
        console.log('   按 3 键 - 切换到 Battle 场景');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🎮 ===================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.switchToLaunch();
                break;
            case KeyCode.DIGIT_2:
                this.switchToMain();
                break;
            case KeyCode.DIGIT_3:
                this.switchToBattle();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 切换到启动场景
     */
    private switchToLaunch(): void {
        console.log('🚀 切换到启动场景');
        this.switchScene('Launch');
    }

    /**
     * 切换到主界面场景
     */
    private switchToMain(): void {
        console.log('🏠 切换到主界面场景');
        this.switchScene('Main');
    }

    /**
     * 切换到战斗场景
     */
    private switchToBattle(): void {
        console.log('⚔️ 切换到战斗场景');
        this.switchScene('Battle');
    }

    /**
     * 通用场景切换方法
     */
    private switchScene(sceneName: string): void {
        try {
            console.log(`🔄 正在切换到场景: ${sceneName}`);
            director.loadScene(sceneName, (error) => {
                if (error) {
                    console.error(`❌ 场景切换失败: ${sceneName}`, error);
                } else {
                    console.log(`✅ 场景切换成功: ${sceneName}`);
                }
            });
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    protected onDestroy(): void {
        // 清理事件监听
        const eventManager = EventManager.getInstance();
        eventManager.off('area-selected', this.onAreaSelected, this);
        eventManager.off('character-function-selected', this.onCharacterFunctionSelected, this);
        eventManager.off('character-info-updated', this.onCharacterInfoUpdated, this);

        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🗑️ MainScene: 主场景销毁');
    }
}
