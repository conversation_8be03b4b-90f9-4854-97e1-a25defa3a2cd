import { _decorator, Component, Node, Label, ProgressBar, Sprite, Color, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

/**
 * 战斗场景控制器
 * 负责管理战斗场景的UI更新和逻辑控制
 */
@ccclass('BattleScene')
export class BattleScene extends Component {
    
    @property(Label)
    roundLabel: Label = null!;
    
    @property(Label)
    timeLabel: Label = null!;
    
    @property(Node)
    playerArea: Node = null!;
    
    @property(Node)
    enemyArea: Node = null!;
    
    // 战斗状态
    private battleActive: boolean = false;
    private battleTime: number = 0;
    private currentRound: number = 1;
    
    // 角色单元数组
    private playerUnits: CharacterUnit[] = [];
    private enemyUnits: CharacterUnit[] = [];
    
    start() {
        this.initializeBattle();
    }
    
    update(deltaTime: number) {
        if (this.battleActive) {
            this.battleTime += deltaTime;
            this.updateTimeDisplay();
            this.updateCharacterUnits(deltaTime);
        }
    }
    
    /**
     * 初始化战斗
     */
    private initializeBattle() {
        this.battleActive = true;
        this.battleTime = 0;
        this.currentRound = 1;
        
        // 更新UI显示
        this.updateRoundDisplay();
        this.updateTimeDisplay();
        
        // 收集场景中的角色单元
        this.collectCharacterUnits();
        
        console.log('战斗开始！');
    }
    
    /**
     * 收集场景中的角色单元
     */
    private collectCharacterUnits() {
        // 收集玩家单元
        if (this.playerArea) {
            const playerNodes = this.playerArea.children;
            for (let node of playerNodes) {
                const unit = node.getComponent(CharacterUnit);
                if (unit) {
                    this.playerUnits.push(unit);
                    unit.setTeam('player');
                }
            }
        }
        
        // 收集敌人单元
        if (this.enemyArea) {
            const enemyNodes = this.enemyArea.children;
            for (let node of enemyNodes) {
                const unit = node.getComponent(CharacterUnit);
                if (unit) {
                    this.enemyUnits.push(unit);
                    unit.setTeam('enemy');
                }
            }
        }
        
        console.log(`收集到 ${this.playerUnits.length} 个玩家单元，${this.enemyUnits.length} 个敌人单元`);
    }
    
    /**
     * 更新回合显示
     */
    private updateRoundDisplay() {
        if (this.roundLabel) {
            this.roundLabel.string = `回合: ${this.currentRound}`;
        }
    }
    
    /**
     * 更新时间显示
     */
    private updateTimeDisplay() {
        if (this.timeLabel) {
            const minutes = Math.floor(this.battleTime / 60);
            const seconds = Math.floor(this.battleTime % 60);
            this.timeLabel.string = `时间: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    /**
     * 更新角色单元
     */
    private updateCharacterUnits(deltaTime: number) {
        // 更新玩家单元
        for (let unit of this.playerUnits) {
            unit.updateUnit(deltaTime);
        }
        
        // 更新敌人单元
        for (let unit of this.enemyUnits) {
            unit.updateUnit(deltaTime);
        }
    }
    
    /**
     * 撤退按钮点击事件
     */
    public onRetreatButtonClicked() {
        console.log('玩家选择撤退');
        this.endBattle(false);
    }
    
    /**
     * 结束战斗
     */
    private endBattle(victory: boolean) {
        this.battleActive = false;
        
        if (victory) {
            console.log('战斗胜利！');
        } else {
            console.log('战斗结束');
        }
        
        // TODO: 处理战斗结束逻辑，如返回主菜单、显示结果等
    }
}

/**
 * 角色单元组件
 * 控制单个角色的显示和行为
 */
@ccclass('CharacterUnit')
export class CharacterUnit extends Component {
    
    @property(Label)
    nameLabel: Label = null!;
    
    @property(Label)
    hpLabel: Label = null!;
    
    @property(Label)
    mpLabel: Label = null!;
    
    @property(Label)
    actionLabel: Label = null!;
    
    @property(ProgressBar)
    hpBar: ProgressBar = null!;
    
    @property(ProgressBar)
    mpBar: ProgressBar = null!;
    
    @property(ProgressBar)
    actionBar: ProgressBar = null!;
    
    @property(Sprite)
    characterImage: Sprite = null!;
    
    // 角色属性
    private characterName: string = '战士';
    private maxHp: number = 210;
    private currentHp: number = 210;
    private maxMp: number = 20;
    private currentMp: number = 20;
    private actionProgress: number = 0;
    private actionSpeed: number = 0.1; // 行动条增长速度
    private team: string = 'player';
    private isAlive: boolean = true;
    
    start() {
        this.initializeUnit();
    }
    
    /**
     * 初始化角色单元
     */
    private initializeUnit() {
        this.updateDisplay();
    }
    
    /**
     * 设置队伍
     */
    public setTeam(team: string) {
        this.team = team;
        
        // 根据队伍设置不同的外观
        if (team === 'enemy') {
            // 敌人使用不同的颜色
            if (this.characterImage) {
                this.characterImage.color = new Color(150, 100, 50, 255);
            }
        }
    }
    
    /**
     * 更新角色单元
     */
    public updateUnit(deltaTime: number) {
        if (!this.isAlive) return;
        
        // 更新行动条
        this.actionProgress += this.actionSpeed * deltaTime;
        if (this.actionProgress >= 1.0) {
            this.actionProgress = 1.0;
            this.performAction();
        }
        
        this.updateDisplay();
    }
    
    /**
     * 执行行动
     */
    private performAction() {
        console.log(`${this.characterName} 执行行动`);
        
        // 重置行动条
        this.actionProgress = 0;
        
        // TODO: 实现具体的行动逻辑（攻击、技能等）
    }
    
    /**
     * 更新显示
     */
    private updateDisplay() {
        // 更新名称
        if (this.nameLabel) {
            this.nameLabel.string = this.characterName;
        }
        
        // 更新HP显示
        if (this.hpLabel) {
            this.hpLabel.string = `${this.currentHp}/${this.maxHp}`;
        }
        if (this.hpBar) {
            this.hpBar.progress = this.currentHp / this.maxHp;
        }
        
        // 更新MP显示
        if (this.mpLabel) {
            this.mpLabel.string = `${this.currentMp}/${this.maxMp}`;
        }
        if (this.mpBar) {
            this.mpBar.progress = this.currentMp / this.maxMp;
        }
        
        // 更新行动条显示
        if (this.actionBar) {
            this.actionBar.progress = this.actionProgress;
        }
    }
    
    /**
     * 设置角色数据
     */
    public setCharacterData(name: string, maxHp: number, maxMp: number) {
        this.characterName = name;
        this.maxHp = maxHp;
        this.currentHp = maxHp;
        this.maxMp = maxMp;
        this.currentMp = maxMp;
        this.updateDisplay();
    }
    
    /**
     * 受到伤害
     */
    public takeDamage(damage: number) {
        this.currentHp = Math.max(0, this.currentHp - damage);
        
        if (this.currentHp <= 0) {
            this.die();
        }
        
        // 伤害数字动画
        this.showDamageNumber(damage);
        
        this.updateDisplay();
    }
    
    /**
     * 显示伤害数字
     */
    private showDamageNumber(damage: number) {
        // TODO: 实现伤害数字飘字效果
        console.log(`${this.characterName} 受到 ${damage} 点伤害`);
    }
    
    /**
     * 死亡
     */
    private die() {
        this.isAlive = false;
        console.log(`${this.characterName} 死亡`);
        
        // 死亡动画
        tween(this.node)
            .to(0.5, { scale: new Vec3(0.8, 0.8, 1) })
            .call(() => {
                this.node.active = false;
            })
            .start();
    }
}
